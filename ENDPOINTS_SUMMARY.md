# Cortexa Platform Endpoints Summary

## Overview
This document provides a comprehensive list of all HTTP and WebSocket endpoints available in the Cortexa platform, both through Traefik gateway and direct access.

## Traefik Gateway Endpoints (Port 80)

### UI Service
- **URL**: `http://localhost/`
- **Description**: Main web UI for voice gateway client
- **Internal**: `nginx:80` serving static files from `services.ui/`
- **Priority**: 1 (lowest, catches all unmatched routes)

### Voice Gateway Service
- **Base URL**: `http://localhost/api/v1/voice-gateway`
- **Internal Service**: `voice-gateway:8002`
- **Available Endpoints**:
  - `GET /api/v1/voice-gateway/health` → Health check endpoint
  - `GET /api/v1/voice-gateway/docs` → FastAPI Swagger documentation
  - `GET /api/v1/voice-gateway/redoc` → ReDoc documentation
  - `GET /api/v1/voice-gateway/openapi.json` → OpenAPI specification
  - `GET /api/v1/voice-gateway/metrics` → Prometheus metrics

### Monitoring Services

#### Traefik Dashboard
- **URL**: `http://localhost/traefik`
- **Description**: Traefik dashboard and API
- **Internal**: Traefik API service

#### Kafka UI
- **URL**: `http://localhost/kafka-ui`
- **Description**: Kafka cluster management interface
- **Internal**: `kafka-ui:8080`

#### Prometheus
- **URL**: `http://localhost/prometheus`
- **Description**: Metrics collection and querying
- **Internal**: `prometheus:9090`

#### Grafana
- **URL**: `http://localhost/grafana`
- **Description**: Metrics visualization and dashboards
- **Internal**: `grafana:3000`
- **Credentials**: admin/admin

#### Tempo
- **URL**: `http://localhost/tempo`
- **Description**: Distributed tracing backend
- **Internal**: `tempo:3200`

## WebSocket Endpoints (Port 8000)

### Voice Gateway WebSocket
- **URL**: `ws://localhost:8000/ws/v1/voice-gateway/call/{call_id}`
- **Description**: Real-time voice translation WebSocket connection
- **Internal**: `ws://voice-gateway:8002/api/v1/call/ws/call/{call_id}`
- **Authentication**: Required (JWT token in headers)
- **Path Transformation**: 
  - External: `/ws/v1/voice-gateway/call/{call_id}`
  - Internal: `/api/v1/call/ws/call/{call_id}`

## Direct Access Endpoints (Development/Internal)

### Tracing and Monitoring (OTLP)
- **Tempo OTLP gRPC**: `localhost:4317`
- **Tempo OTLP HTTP**: `localhost:4318`

### Message Queue
- **Kafka**: `localhost:9092`

## Service Architecture

### Voice Gateway Service (Port 8002)
**FastAPI Application** with the following structure:
- **Base Path**: `/api/v1`
- **Health Endpoint**: `/health`
- **Call WebSocket**: `/call/ws/call/{call_id}`
- **Metrics**: `/metrics` (Prometheus format)
- **Documentation**: `/docs`, `/redoc`, `/openapi.json`

### Traefik Configuration
**Entry Points**:
- `web`: Port 80 (HTTP traffic)
- `ws`: Port 8000 (WebSocket traffic)

**Middleware**:
- Path stripping for clean internal routing
- JWT authentication middleware (configured but not actively used)

## Security Notes

1. **Authentication**: WebSocket connections require JWT authentication
2. **CORS**: Voice Gateway has permissive CORS settings (configure for production)
3. **Monitoring Access**: Direct access to monitoring tools (secure in production)

## Missing/Potential Endpoints

Based on the codebase analysis, all identified endpoints are properly configured. The configuration includes:

1. ✅ Voice Gateway HTTP API endpoints
2. ✅ Voice Gateway WebSocket endpoints  
3. ✅ UI service for web interface
4. ✅ All monitoring and observability tools
5. ✅ Proper Traefik routing and middleware

## Configuration Improvements Made

1. **Added UI Service**: Containerized the web UI with nginx
2. **Unified Gateway Access**: All services accessible through Traefik on port 80
3. **Cleaned Up Labels**: Organized Traefik labels for better readability
4. **Added Missing Routes**: Exposed monitoring tools through gateway
5. **Proper Middleware**: Configured path transformation for clean routing

## Usage Examples

### Health Check
```bash
curl http://localhost/api/v1/voice-gateway/health
```

### WebSocket Connection (JavaScript)
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/v1/voice-gateway/call/test-call-123');
```

### Access Monitoring
- Grafana: http://localhost/grafana
- Prometheus: http://localhost/prometheus  
- Kafka UI: http://localhost/kafka-ui
- Traefik Dashboard: http://localhost/traefik
